import { type PrismaClient } from '@prisma/client'
import {
    type AiScraperConfig,
    type ScraperOptions,
    aiScraperConfigSchema
} from './schemas'
import { type CreateProduct } from '../product/schemas'
import { createProductService } from '../product/service'
import { createAiGenericScraper } from './implementations/listings/aiGeneric'
import { testOllamaConnection } from './utils/aiScraper'

/**
 * Service for AI-powered scraping operations
 */
export function createAiScraperService(db: PrismaClient) {
    /**
     * Test connection to Ollama server
     */
    const testConnection = async (
        config: AiScraperConfig
    ): Promise<boolean> => {
        return await testOllamaConnection(config)
    }

    /**
     * Scrape a single URL using AI
     */
    const scrapeUrl = async (
        url: string,
        config: AiScraperConfig,
        options?: ScraperOptions
    ): Promise<CreateProduct[]> => {
        console.log(`[AI SCRAPER SERVICE] Starting to scrape URL: ${url}`)

        const scraper = createAiGenericScraper(config)
        const products = await scraper.scrapeUrl(url, options)

        console.log(
            `[AI SCRAPER SERVICE] Extracted ${products.length} products from ${url}`
        )
        return products
    }

    /**
     * Scrape a URL and save products to database
     */
    const scrapeAndSave = async (
        url: string,
        config: AiScraperConfig,
        options?: ScraperOptions
    ): Promise<{
        productsFound: number
        productsSaved: number
        errors: string[]
    }> => {
        console.log(
            `[AI SCRAPER SERVICE] Starting scrape and save for URL: ${url}`
        )

        const errors: string[] = []
        let productsSaved = 0

        try {
            const products = await scrapeUrl(url, config, options)
            const productService = createProductService(db)

            // Save each product to the database
            for (const product of products) {
                try {
                    await productService.upsert(product)
                    productsSaved++
                } catch (error) {
                    const errorMessage = `Failed to save product ${product.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
                    console.error(`[AI SCRAPER SERVICE] ${errorMessage}`)
                    errors.push(errorMessage)
                }
            }

            console.log(
                `[AI SCRAPER SERVICE] Saved ${productsSaved}/${products.length} products from ${url}`
            )

            return {
                productsFound: products.length,
                productsSaved,
                errors
            }
        } catch (error) {
            const errorMessage = `Failed to scrape URL ${url}: ${error instanceof Error ? error.message : 'Unknown error'}`
            console.error(`[AI SCRAPER SERVICE] ${errorMessage}`)
            errors.push(errorMessage)

            return {
                productsFound: 0,
                productsSaved: 0,
                errors
            }
        }
    }

    /**
     * Scrape multiple URLs with the same configuration
     */
    const scrapeMultipleUrlsAndSave = async (
        urls: string[],
        config: AiScraperConfig,
        options?: ScraperOptions
    ): Promise<{
        totalProductsFound: number
        totalProductsSaved: number
        urlResults: Array<{
            url: string
            productsFound: number
            productsSaved: number
        }>
        errors: string[]
    }> => {
        console.log(
            `[AI SCRAPER SERVICE] Starting batch scrape for ${urls.length} URLs`
        )

        const urlResults: Array<{
            url: string
            productsFound: number
            productsSaved: number
        }> = []
        const allErrors: string[] = []
        let totalProductsFound = 0
        let totalProductsSaved = 0

        for (const url of urls) {
            const result = await scrapeAndSave(url, config, options)

            urlResults.push({
                url,
                productsFound: result.productsFound,
                productsSaved: result.productsSaved
            })

            totalProductsFound += result.productsFound
            totalProductsSaved += result.productsSaved
            allErrors.push(...result.errors)

            // Add delay between URLs
            if (urls.indexOf(url) < urls.length - 1) {
                const delay = Math.floor(Math.random() * 5000) + 3000
                console.log(
                    `[AI SCRAPER SERVICE] Waiting ${delay}ms before next URL...`
                )
                await new Promise((resolve) => setTimeout(resolve, delay))
            }
        }

        console.log(
            `[AI SCRAPER SERVICE] Completed batch scrape. Total: ${totalProductsSaved}/${totalProductsFound} products saved`
        )

        return {
            totalProductsFound,
            totalProductsSaved,
            urlResults,
            errors: allErrors
        }
    }

    /**
     * Scrape with different configurations for different URLs
     */
    const batchScrapeWithDifferentConfigs = async (
        urlConfigs: Array<{
            url: string
            config: Partial<AiScraperConfig> & {
                endpoint: string
                model: string
            }
        }>,
        options?: ScraperOptions
    ): Promise<{
        totalProductsFound: number
        totalProductsSaved: number
        urlResults: Array<{
            url: string
            productsFound: number
            productsSaved: number
        }>
        errors: string[]
    }> => {
        console.log(
            `[AI SCRAPER SERVICE] Starting batch scrape with ${urlConfigs.length} different configurations`
        )

        const urlResults: Array<{
            url: string
            productsFound: number
            productsSaved: number
        }> = []
        const allErrors: string[] = []
        let totalProductsFound = 0
        let totalProductsSaved = 0

        for (const { url, config } of urlConfigs) {
            // Parse config through schema to apply defaults
            const fullConfig = aiScraperConfigSchema.parse(config)
            const result = await scrapeAndSave(url, fullConfig, options)

            urlResults.push({
                url,
                productsFound: result.productsFound,
                productsSaved: result.productsSaved
            })

            totalProductsFound += result.productsFound
            totalProductsSaved += result.productsSaved
            allErrors.push(...result.errors)

            // Add delay between URLs
            if (urlConfigs.indexOf({ url, config }) < urlConfigs.length - 1) {
                const delay = Math.floor(Math.random() * 5000) + 3000
                await new Promise((resolve) => setTimeout(resolve, delay))
            }
        }

        console.log(
            `[AI SCRAPER SERVICE] Completed batch scrape with different configs. Total: ${totalProductsSaved}/${totalProductsFound} products saved`
        )

        return {
            totalProductsFound,
            totalProductsSaved,
            urlResults,
            errors: allErrors
        }
    }

    return {
        testConnection,
        scrapeUrl,
        scrapeAndSave,
        scrapeMultipleUrlsAndSave,
        batchScrapeWithDifferentConfigs
    }
}
