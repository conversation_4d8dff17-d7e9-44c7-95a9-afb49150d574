import { type AiListingScraper } from '../../types'
import { type CreateProduct } from '../../../product/schemas'
import { navigateWithStealth, safelyCloseResources } from '../../browser'
import { type ScraperOptions, type AiScraperConfig } from '../../schemas'
import {
    extractProductsWithAi,
    testOllamaConnection
} from '../../utils/aiScraper'
import { addRandomDelay } from '../../utils/humanBehavior'

/**
 * Generic AI-powered scraper that can extract products from any webpage
 */
export class AiGenericListingScraper implements AiListingScraper {
    public readonly name = 'AI Generic Listing Scraper'
    public readonly config: AiScraperConfig

    constructor(config: AiScraperConfig) {
        this.config = config
    }

    /**
     * Scrape any webpage using AI to extract product information
     */
    async scrapeUrl(
        url: string,
        options?: ScraperOptions
    ): Promise<CreateProduct[]> {
        console.log(`[AI SCRAPER] Starting to scrape URL: ${url}`)

        // Test Ollama connection first
        const isConnected = await testOllamaConnection(this.config)
        if (!isConnected) {
            throw new Error(
                `Cannot connect to Ollama at ${this.config.endpoint}`
            )
        }

        // Navigate to the page with stealth
        const { browser, context, page } = await navigateWithStealth(
            url,
            options
        )

        try {
            // Wait for the page to load completely
            await page.waitForLoadState('networkidle', {
                timeout: options?.timeout || 30000
            })

            // Add random delay to appear more human-like
            await addRandomDelay(page, 1000, 3000)

            // Get the full HTML content
            const htmlContent = await page.content()

            console.log(
                `[AI SCRAPER] Extracted HTML content (${htmlContent.length} characters)`
            )

            // Use AI to extract product information
            const products = await extractProductsWithAi(
                htmlContent,
                this.config
            )

            console.log(
                `[AI SCRAPER] AI extracted ${products.length} products from ${url}`
            )

            return products
        } catch (error) {
            console.error(`[AI SCRAPER] Error scraping URL ${url}:`, error)
            throw error
        } finally {
            // Safely close all resources
            await safelyCloseResources(page, context, browser, options)
        }
    }
}

/**
 * Create a new AI generic scraper instance
 */
export function createAiGenericScraper(
    config: AiScraperConfig
): AiGenericListingScraper {
    return new AiGenericListingScraper(config)
}

/**
 * Default AI scraper configuration for quick setup
 */
export const defaultAiScraperConfig: AiScraperConfig = {
    endpoint: 'http://**********:11434',
    model: 'gemma3:12b',
    temperature: 0.1,
    maxTokens: 4096,
    instructions:
        'Focus on 3D printing filaments and materials. Extract all available product details including specifications.'
}

/**
 * Scrape multiple URLs with the same AI configuration
 */
export async function scrapeMultipleUrls(
    urls: string[],
    config: AiScraperConfig,
    options?: ScraperOptions
): Promise<CreateProduct[]> {
    console.log(`[AI SCRAPER] Starting to scrape ${urls.length} URLs`)

    const scraper = createAiGenericScraper(config)
    const allProducts: CreateProduct[] = []

    for (const url of urls) {
        try {
            const products = await scraper.scrapeUrl(url, options)
            allProducts.push(...products)

            // Add delay between URLs to avoid being blocked
            const delay = Math.floor(Math.random() * 5000) + 3000
            console.log(`[AI SCRAPER] Waiting ${delay}ms before next URL...`)
            await new Promise((resolve) => setTimeout(resolve, delay))
        } catch (error) {
            console.error(`[AI SCRAPER] Error scraping URL ${url}:`, error)
            // Continue with other URLs even if one fails
        }
    }

    console.log(
        `[AI SCRAPER] Completed scraping ${urls.length} URLs. Total products: ${allProducts.length}`
    )
    return allProducts
}

/**
 * Batch scrape with different configurations for different URLs
 */
export async function batchScrapeWithConfigs(
    urlConfigs: Array<{ url: string; config: AiScraperConfig }>,
    options?: ScraperOptions
): Promise<CreateProduct[]> {
    console.log(
        `[AI SCRAPER] Starting batch scrape of ${urlConfigs.length} URL configurations`
    )

    const allProducts: CreateProduct[] = []

    for (const { url, config } of urlConfigs) {
        try {
            const scraper = createAiGenericScraper(config)
            const products = await scraper.scrapeUrl(url, options)
            allProducts.push(...products)

            // Add delay between URLs
            const delay = Math.floor(Math.random() * 5000) + 3000
            await new Promise((resolve) => setTimeout(resolve, delay))
        } catch (error) {
            console.error(
                `[AI SCRAPER] Error in batch scrape for URL ${url}:`,
                error
            )
        }
    }

    console.log(
        `[AI SCRAPER] Completed batch scrape. Total products: ${allProducts.length}`
    )
    return allProducts
}
